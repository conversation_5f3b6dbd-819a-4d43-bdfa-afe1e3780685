import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { IAppRes } from '../models/bug-report.model';

@Injectable({
  providedIn: 'root'
})
export class AppliedJobService {

  private baseUrl = 'http://localhost:5001/api/appliedJob';
    
  constructor(private http: HttpClient) {}
  
  createAppliedJob(formData: FormData): Observable<any> {
    return this.http.post(`${this.baseUrl}`, formData);
  }

  getAllAppliedJobs(pageNumber?: number, pageSize?: number): Observable<IAppRes> {
    let params = new HttpParams();
    if (pageNumber !== undefined && pageSize !== undefined) {
      params = params.set('pageNumber', pageNumber.toString());
      params = params.set('pageSize', pageSize.toString());
    }
    return this.http.get<IAppRes>(this.baseUrl, { params });
  }

  getAppliedJobById(id: string): Observable<IAppRes> {
    return this.http.get<IAppRes>(`${this.baseUrl}/${id}`);
  }

  deleteAppliedJob(id: string): Observable<IAppRes> {
    return this.http.delete<IAppRes>(`${this.baseUrl}/${id}`);
  }

}
