 export interface Bug {
  id: number;
  title: string;
  description: string;
  createdBy: string;
  reportedDate: string;
  priority: 'High' | 'Medium' | 'Low';
  status: 'Open' | 'In Progress' | 'Closed';
}

export interface AppManagementEntityDto {
  id?: string;
  title: string;
  description?: string;
  logoUrl?: string;
  websiteUrl?: string;
  storeUrl?: string;
}

export interface BugReportingEntityDto {
  id?: string;
  appId?: string;
  appManagement?: AppManagementEntityDto;
  title: string;
  description?: string;
  status?: string;
  priority?: string;
  createdAt?: Date;
}

export interface PagedResult<T> {
  items: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
}

export interface IAppRes<T = {}> {
  msg: string;
  auth: IAppRes_Auth;
  data: T;
}

export interface IAppRes_Auth {
  success: boolean;
  token?: string;
  redirectUrl?: string;
  statusCode?: number;
  userPref?: {
    email?: string;
    firstName?: string;
    lastName?: string;
  };
}
