import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { IAppRes } from '../models/bug-report.model';

@Injectable({
  providedIn: 'root'
})
export class JobService {
  incrementApplication(jobId: any) {
    throw new Error('Method not implemented.');
  }

   private baseUrl = 'http://localhost:5001/api/jobs';

  constructor(private http: HttpClient) {}

  getJobs(pageNumber?: number, pageSize?: number): Observable<IAppRes> {
    let params = new HttpParams();
    if (pageNumber !== undefined) {
      params = params.set('pageNumber', pageNumber.toString());
    }
    if (pageSize !== undefined) {
      params = params.set('pageSize', pageSize.toString());
    }
    return this.http.get<IAppRes>(this.baseUrl, { params });
  }

  getJobById(id: number): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/${id}`);
  }

  createJob(jobData: any): Observable<any> {
    return this.http.post<any>(this.baseUrl, jobData);
  }

  updateJob(jobId: number, job: any): Observable<any> {
    return this.http.put(`${this.baseUrl}/${jobId}`, job);
  }

   deleteJob(id: number): Observable<any> {
    return this.http.delete(`${this.baseUrl}/${id}`);
  }

}
